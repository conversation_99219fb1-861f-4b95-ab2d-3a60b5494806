# == Schema Information
#
# Table name: line_items
#
#  id         :bigint           not null, primary key
#  gift_card  :boolean          default(FALSE), not null
#  points     :integer          default(0)
#  quantity   :integer          default(1), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  cart_id    :bigint
#  order_id   :bigint
#  product_id :bigint
#
# Indexes
#
#  index_line_items_on_cart_id     (cart_id)
#  index_line_items_on_order_id    (order_id)
#  index_line_items_on_product_id  (product_id)
#
class LineItem < ApplicationRecord
  before_save :update_points
  before_save :is_gift_card

  belongs_to :product
  belongs_to :cart, optional: true
  belongs_to :order, optional: true

  validates :quantity, :product, presence: true

  counter_culture :cart, column_name: proc { |model| model.cart.present? ? "subtotal" : nil }, delta_column: "points"

  private

  def update_points
    self.points = quantity * product.points_needed_for_country(Current.country.id)
  end

  def is_gift_card
    self.gift_card = product.gift_card
  end
end
