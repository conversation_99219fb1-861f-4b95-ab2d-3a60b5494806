# == Schema Information
#
# Table name: orders
#
#  id            :bigint           not null, primary key
#  approved_at   :date
#  declined_at   :datetime
#  notes         :text
#  points        :integer          default(0), not null
#  ship_to       :integer          default("home"), not null
#  status        :integer          default("pending")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint
#  ahoy_visit_id :bigint
#  brand_id      :bigint           not null
#  region_id     :bigint           not null
#  sap_id        :string
#  user_id       :bigint           not null
#
# Indexes
#
#  index_orders_on_admin_id   (admin_id)
#  index_orders_on_brand_id   (brand_id)
#  index_orders_on_region_id  (region_id)
#  index_orders_on_user_id    (user_id)
#
class Order < ApplicationRecord
  FILTER_FIELDS = %w[status brand_name created_at country_name].freeze

  include MeiliSearch::Rails
  extend Pagy::Meilisearch
  ActiveRecord_Relation.include Pagy::Meilisearch

  before_update :update_timestamps

  after_create_commit :notify_admins
  after_update_commit :notify_user
  after_create_commit :set_activity

  enum :status, pending: 0, processed: 1, shipped: 2, canceled: 3, approved: 4, declined: 5
  enum :ship_to, home: 0, work: 1

  belongs_to :user
  belongs_to :admin, class_name: "User", optional: true
  belongs_to :brand
  belongs_to :region

  has_many :line_items, -> { includes(:product) }, dependent: :destroy
  has_many :notations, as: :noteable, dependent: :destroy, class_name: "Note"

  validates :user, :brand, :region, :points, presence: true

  has_one :activity, as: :transactable, dependent: :destroy

  visitable :ahoy_visit

  has_paper_trail

  delegate :name, to: :user, prefix: true
  delegate :name, to: :admin, prefix: true, allow_nil: true
  delegate :name, to: :brand, prefix: true

  accepts_nested_attributes_for :notations

  def name
    "Order by #{user.name} on #{created_at}"
  end

  scope :meilisearch_import, -> { includes(:brand, user: [address: :country]) }

  meilisearch do
    attribute :status, :brand_name, :created_at, :user_name, :user_id, :brand_id, :region_id

    attribute :country_name do
      user.address.country.name
    end

    filterable_attributes [:status, :brand_name, :created_at, :user_id, :brand_id, :region_id, :country_name]
    sortable_attributes [:created_at]
  end

  private

  def notify_user
    UpdatedOrderNotifier.with(order: self).deliver_later(user)
  end

  def notify_admins
    NewOrderNotifier.with(order: self).deliver_later(User.admins)
  end

  def set_activity
    create_activity(wallet: user.wallet, kind: "debit", transactable: self)
  end

  def update_timestamps
    if status_changed?
      if approved?
        self.approved_at = Time.zone.now
        activity.approved!

        user.wallet.debit!(points)
      elsif declined?
        self.declined_at = Time.zone.now
        activity.declined!
      end
    end
  end
end
