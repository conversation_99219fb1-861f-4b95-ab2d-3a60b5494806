# == Schema Information
#
# Table name: stores
#
#  id                 :bigint           not null, primary key
#  account_number     :string
#  city               :string
#  name               :string           not null
#  phone_number       :string
#  status             :integer          default("active"), not null
#  street             :string
#  unit               :string
#  users_count        :integer          default(0), not null
#  verified           :boolean
#  zip                :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  account_channel_id :bigint
#  account_type_id    :bigint
#  brand_id           :bigint
#  old_store_id       :integer
#  state_id           :bigint
#  store_chain_id     :bigint
#
# Indexes
#
#  index_stores_on_account_channel_id  (account_channel_id)
#  index_stores_on_account_type_id     (account_type_id)
#  index_stores_on_brand_id            (brand_id)
#  index_stores_on_state_id            (state_id)
#  index_stores_on_store_chain_id      (store_chain_id)
#
class Store < ApplicationRecord
  FILTER_FIELDS = %w[brand_name state_abbr status country_name].freeze
  SORT_TO_GROUP = {state_name: "state_name", brand_name: "brand_name", region_name: "region_name", store_chain: "store_chain_name"}.freeze

  include MeiliSearch::Rails
  extend Pagy::Meilisearch
  ActiveRecord_Relation.include Pagy::Meilisearch

  belongs_to :brand
  belongs_to :store_chain, optional: true

  has_many :users
  has_many :messages, as: :messageable
  has_many :promotion_locations, as: :promotable, dependent: :destroy
  has_many :promotions, through: :promotion_locations

  has_one :address, -> { includes :state }, as: :addressable
  has_one :state, through: :address
  has_one :region, through: :state

  validates :name, presence: true

  counter_culture :store_chain

  enum :status, active: 0, disabled: 1, request: 2

  scope :order_by_name, -> { order(:name) }

  normalizes :phone, with: ->(phone) { phone.delete("^0-9") }

  after_save -> { users.each(&:touch) }
  after_create_commit -> { NewStoreRequestNotifier.with(request: self).deliver_later(User.admins) }, if: :request?

  accepts_nested_attributes_for :address

  delegate :name, to: :brand, prefix: true, allow_nil: true
  delegate :name, to: :state, prefix: true, allow_nil: true
  delegate :name, to: :store_chain, prefix: true, allow_nil: true

  scope :meilisearch_import, -> { includes(:brand, :store_chain, :region, address: [state: :region]) }

  meilisearch do
    attribute :name, :brand_name, :created_at, :brand_id, :_geo, :status

    attribute :region_name do
      address&.state&.region&.name || "Unknown"
    end

    attribute :state_abbr do
      address&.state&.abbreviation || "N/A"
    end

    attribute :state_name do
      address&.state&.name || "Unknown"
    end

    attribute :store_chain do
      store_chain_name || "Independent"
    end

    attribute :region_id do
      address&.state&.region&.id || nil
    end

    attribute :city_name do
      address&.city || "Unknown"
    end

    attribute :country_name do
      address&.country&.name || "Unknown"
    end

    filterable_attributes [:brand_name, :state_abbr, :region_name, :region_id, :brand_id, :_geo, :status, :country_name]
    sortable_attributes [:state_name, :brand_name, :region_name, :store_chain, :name]
  end

  def region_admin
    state.region.admin
  end

  def name_with_address
    "#{name} (#{address.line1})"
  end

  def name_with_city
    "#{name} (#{address.line1}, #{address.city})"
  end

  def self.group_name
    "Store"
  end

  def _geo
    return nil if address.blank?

    {
      lat: address.latitude,
      lng: address.longitude
    }
  end
end
