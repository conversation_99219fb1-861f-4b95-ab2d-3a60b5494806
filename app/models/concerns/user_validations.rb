module UserValidations
  extend ActiveSupport::Concern

  included do
    validates :phone_number, :first_name, :last_name, presence: true
    validates :phone_number, format: {
      with: /\A[\+]?[1-9][\d]{0,15}\z/,
      message: "must be a valid phone number"
    }
    validates :email, format: {
      with: URI::MailTo::EMAIL_REGEXP,
      message: "must be a valid email address"
    }

    validate :valid_status_transition, if: :status_changed?
    validate :admin_region_matches_role
    validate :admin_brand_matches_role
  end

  private

  def valid_status_transition
    return unless status_was.present?

    valid_transitions = {
      "new" => ["active", "inactive"],
      "active" => ["inactive", "deleted"],
      "inactive" => ["active", "deleted"],
      "deleted" => [] # No transitions from deleted
    }

    unless valid_transitions[status_was]&.include?(status)
      errors.add(:status, "cannot transition from #{status_was} to #{status}")
    end
  end

  def admin_region_matches_role
    return unless admin_region_id.present?

    unless region_admin? || admin? || super_admin?
      errors.add(:admin_region, "can only be assigned to region admins or higher")
    end
  end

  def admin_brand_matches_role
    return unless admin_brand_id.present?

    unless admin? || super_admin?
      errors.add(:admin_brand, "can only be assigned to admins or super admins")
    end
  end
end
