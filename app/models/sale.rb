# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  notes         :text
#  origin        :integer          default("internet")
#  points        :integer
#  serial_number :string
#  sold_at       :datetime
#  status        :integer          default("pending")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint
#  ahoy_visit_id :bigint
#  brand_id      :bigint
#  product_id    :bigint           not null
#  promotion_id  :bigint
#  region_id     :bigint
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_admin_id      (admin_id)
#  index_sales_on_brand_id      (brand_id)
#  index_sales_on_product_id    (product_id)
#  index_sales_on_promotion_id  (promotion_id)
#  index_sales_on_region_id     (region_id)
#  index_sales_on_user_id       (user_id)
#
class Sale < ApplicationRecord
  FILTER_FIELDS = %w[brand_name status sold_at origin country_name].freeze

  include MeiliSearch::Rails
  extend Pagy::Meilisearch
  ActiveRecord_Relation.include Pagy::Meilisearch
  include Flipper::Identifier

  before_save :assign_points

  after_create_commit :notify_admins
  after_create_commit :record_activity

  before_update :update_status

  after_update_commit :notify_user

  enum :status, pending: 0, approved: 1, declined: 2
  enum :origin, internet: 0, in_store: 1

  belongs_to :user, -> { includes :store }
  belongs_to :product
  belongs_to :admin, class_name: "User", optional: true
  belongs_to :brand
  belongs_to :region
  belongs_to :promotion, optional: true

  has_one :activity, as: :transactable, dependent: :destroy

  has_many :notations, as: :noteable, dependent: :destroy, class_name: "Note"

  validates :serial_number, :status, :sold_at, presence: true
  validates :serial_number, uniqueness: {case_sensitive: false}, numericality: {only_integer: true}, list: true, length: {minimum: 7}
  validates :sold_at, timeliness: {type: :date, on_or_before: :today}

  visitable :ahoy_visit

  has_paper_trail

  delegate :name, to: :product, prefix: true, allow_nil: true
  delegate :name, to: :user, prefix: true
  delegate :name, to: :admin, prefix: true, allow_nil: true
  delegate :name, to: :brand, prefix: true

  accepts_nested_attributes_for :notations

  has_one_attached :receipt
  validates :receipt, presence: true, if: -> { Current.country == "CA" }

  scope :meilisearch_import, -> { includes(:brand, :product, user: [address: :country]) }

  def name
    "Sale on #{sold_at} by #{user.name}"
  end

  meilisearch do
    attribute :status, :origin, :serial_number, :brand_name, :product_name, :user_id, :brand_id, :region_id

    attribute :sold_at do
      sold_at.to_time.to_i
    end

    attribute :created_at do
      created_at.to_time.to_i
    end

    attribute :country_name do
      user.address.country.name
    end

    filterable_attributes [:brand_name, :status, :sold_at, :origin, :user_id, :brand_id, :region_id, :country_name]
    sortable_attributes [:sold_at, :created_at]
  end

  private

  def assign_points
    self.points = promotion.present? ? (promotion.multiplier * product.points_earned_for_country(Current.country.id)) : product.points_earned_for_country(Current.country.id)
  end

  def notify_admins
    NewSaleNotifier.with(sale: self).deliver_later(User.admins)
  end

  def record_activity
    create_activity(wallet: user.wallet, kind: "credit", transactable: self)
  end

  def notify_user
    if status_changed?
      UpdatedSaleNotifier.with(sale: self).deliver_later(user)
    end
  end

  def update_status
    if status_changed?
      self.approved_at = Time.zone.now
      case status
      when "approved"
        activity.approved!
        user.wallet.credit!(points)
      when "declined"
        activity.declined!
      end
    end
  end
end
