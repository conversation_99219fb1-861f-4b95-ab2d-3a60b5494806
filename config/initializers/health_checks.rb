# Rails 8 Enhanced Health Checks Configuration

Rails.application.configure do
  # Configure Rails 8 health check endpoint
  config.health_check_path = "/up"
  
  # Add custom health checks for Rails 8
  Rails.application.routes.prepend do
    # Enhanced health check with detailed status
    get "/health", to: "health#show"
    get "/health/detailed", to: "health#detailed"
  end
end

# Custom Health Check Controller
class HealthController < ActionController::Base
  # Skip authentication for health checks
  skip_before_action :authenticate_user!, if: :defined_authenticate_user?
  skip_before_action :verify_authenticity_token
  
  def show
    checks = perform_health_checks
    
    if checks.all? { |_, status| status[:healthy] }
      render json: { status: "ok", timestamp: Time.current }, status: :ok
    else
      render json: { 
        status: "error", 
        timestamp: Time.current,
        errors: checks.select { |_, status| !status[:healthy] }
      }, status: :service_unavailable
    end
  end
  
  def detailed
    checks = perform_health_checks
    
    render json: {
      status: checks.all? { |_, status| status[:healthy] } ? "ok" : "error",
      timestamp: Time.current,
      checks: checks,
      version: Rails.application.config.version || "unknown",
      environment: Rails.env
    }
  end
  
  private
  
  def perform_health_checks
    {
      database: check_database,
      redis: check_redis,
      storage: check_storage,
      sidekiq: check_sidekiq,
      meilisearch: check_meilisearch
    }
  end
  
  def check_database
    ActiveRecord::Base.connection.execute("SELECT 1")
    { healthy: true, message: "Database connection successful" }
  rescue => e
    { healthy: false, message: "Database error: #{e.message}" }
  end
  
  def check_redis
    Redis.new(url: ENV.fetch("REDIS_URL", "redis://localhost:6379/0")).ping
    { healthy: true, message: "Redis connection successful" }
  rescue => e
    { healthy: false, message: "Redis error: #{e.message}" }
  end
  
  def check_storage
    ActiveStorage::Blob.service.exist?("health_check_test") rescue false
    { healthy: true, message: "Storage service accessible" }
  rescue => e
    { healthy: false, message: "Storage error: #{e.message}" }
  end
  
  def check_sidekiq
    return { healthy: true, message: "Sidekiq not configured" } unless defined?(Sidekiq)
    
    Sidekiq.redis(&:ping)
    stats = Sidekiq::Stats.new
    
    { 
      healthy: true, 
      message: "Sidekiq operational",
      queues: stats.queues,
      processed: stats.processed,
      failed: stats.failed
    }
  rescue => e
    { healthy: false, message: "Sidekiq error: #{e.message}" }
  end
  
  def check_meilisearch
    return { healthy: true, message: "MeiliSearch not configured" } unless defined?(MeiliSearch)
    
    MeiliSearch::Rails.client.health
    { healthy: true, message: "MeiliSearch operational" }
  rescue => e
    { healthy: false, message: "MeiliSearch error: #{e.message}" }
  end
  
  def defined_authenticate_user?
    respond_to?(:authenticate_user!, true)
  end
end
