# Be sure to restart your server when you modify this file.

# Define an application-wide HTTP permissions policy. For further
# information see: https://developers.google.com/web/updates/2018/06/feature-policy

Rails.application.config.permissions_policy do |policy|
  # Disable potentially dangerous features
  policy.camera :none
  policy.gyroscope :none
  policy.microphone :none
  policy.usb :none
  policy.geolocation :self  # Allow geolocation for store features
  policy.fullscreen :self
  policy.payment :none  # Disable unless you need payment APIs

  # Disable other potentially sensitive features
  policy.accelerometer :none
  policy.ambient_light_sensor :none
  policy.autoplay :none
  policy.battery :none
  policy.clipboard_read :none
  policy.clipboard_write :self
  policy.display_capture :none
  policy.document_domain :none
  policy.encrypted_media :none
  policy.execution_while_not_rendered :none
  policy.execution_while_out_of_viewport :none
  policy.magnetometer :none
  policy.midi :none
  policy.navigation_override :none
  policy.picture_in_picture :none
  policy.publickey_credentials_get :self
  policy.screen_wake_lock :none
  policy.sync_xhr :self
  policy.web_share :none
  policy.xr_spatial_tracking :none
end
