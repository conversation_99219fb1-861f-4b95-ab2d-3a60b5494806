# Rails 8 New Features and Best Practices Configuration

Rails.application.configure do
  # Rails 8 Authentication Features
  # Enable Rails 8 built-in authentication (if migrating from <PERSON><PERSON>)
  # config.authentication.default_protect_from_forgery = true

  # Rails 8 Caching Improvements
  # Enable Rails 8 enhanced fragment caching
  config.action_controller.enable_fragment_cache_logging = Rails.env.development?

  # Rails 8 Active Record Enhancements
  # Enable Rails 8 query optimization features
  config.active_record.query_log_tags_enabled = Rails.env.development?
  config.active_record.query_log_tags = [
    :application, :controller, :action, :job
  ]

  # Enable Rails 8 connection pool improvements
  config.active_record.connection_pool_statistics = Rails.env.production?

  # Rails 8 Action Cable Enhancements
  # Enable Rails 8 Action Cable optimizations
  config.action_cable.mount_path = "/cable"
  config.action_cable.url = Rails.env.production? ?
    "wss://#{ENV.fetch("DOMAIN", "zeisspoints.com")}/cable" :
    "ws://localhost:3000/cable"

  # Rails 8 Active Storage Improvements
  # Enable Rails 8 Active Storage optimizations
  config.active_storage.precompile_assets = Rails.env.production?
  config.active_storage.variant_processor = :mini_magick
  config.active_storage.video_preview_arguments = [
    "-vf", "select=eq(n\\,0)", "-vframes", "1", "-f", "image2", "-"
  ]

  # Rails 8 Action Mailer Enhancements
  # Enable Rails 8 mailer improvements
  config.action_mailer.deliver_later_queue_name = "mailers"
  config.action_mailer.preview_interceptors = [ActionMailer::InlinePreviewInterceptor] if Rails.env.development?

  # Rails 8 Logging Improvements
  # Enable structured logging
  if Rails.env.production?
    config.log_formatter = ActiveSupport::Logger::JSONFormatter.new
    config.log_tags = [
      :request_id,
      ->(request) { "Host: #{request.host}" },
      ->(request) { "User: #{request.env["warden"]&.user&.id}" }
    ]
  end

  # Rails 8 Security Enhancements
  # Enable Rails 8 security features
  config.force_ssl = Rails.env.production?
  if Rails.env.production?
    config.ssl_options = {
      hsts: {
        expires: 1.year,
        subdomains: true,
        preload: true
      }
    }
  end

  # Rails 8 Performance Features
  # Enable Rails 8 performance monitoring
  if Rails.env.production?
    config.active_support.report_deprecations = false
    config.active_record.warn_on_records_fetched_greater_than = 1000
  end

  # Rails 8 Development Features
  if Rails.env.development?
    # Enable Rails 8 development tools
    config.active_record.verbose_query_logs = true
    config.active_record.highlight_dangerous_query_methods = true

    # Enable Rails 8 debugging features
    config.action_view.annotate_rendered_view_with_filenames = true
    config.action_controller.raise_on_missing_callback_actions = true
  end

  # Rails 8 Testing Features
  if Rails.env.test?
    # Enable Rails 8 test optimizations
    config.active_record.maintain_test_schema = true
    config.active_job.queue_adapter = :test
  end
end

# Rails 8 Middleware Configuration
Rails.application.config.middleware.tap do |middleware|
  if Rails.env.production?
    # Add Rails 8 performance middleware
    middleware.use Rack::Deflater
    middleware.use Rack::ETag, "no-cache"

    # Add security middleware
    middleware.use Rack::Attack if defined?(Rack::Attack)
  end

  if Rails.env.development?
    # Add Rails 8 development middleware
    middleware.use Rack::LiveReload if defined?(Rack::LiveReload)
  end
end

# Rails 8 Active Job Configuration
ActiveJob::Base.queue_adapter = Rails.env.production? ? :sidekiq : :async

# Rails 8 Action Cable Configuration
if Rails.env.production?
  ActionCable.server.config.cable = {
    adapter: "redis",
    url: ENV.fetch("REDIS_URL", "redis://localhost:6379/1"),
    channel_prefix: "zeisspoints_production"
  }
end
