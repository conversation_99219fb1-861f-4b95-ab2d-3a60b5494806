source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "~> 3.3"

gem "active_median", "~> 0.4"
gem "active_record_extended", github: "georgekaraszi/activerecordextended"
gem "action_policy", "~> 0.6.0"
gem "administrate"
gem "ahoy_matey", "~> 5.0"
gem "activerecord-import", "~> 1.5"
gem "aws-sdk-s3", "~> 1", require: false
gem "barkick", "~> 0.2.0"
gem "barby", "~> 0.6"
gem "blazer", "~> 3.0"
gem "caxlsx", "~> 4.0"
gem "caxlsx_rails", "~> 0.6"
gem "chartkick", "~> 5.0"
gem "counter_culture", "~> 3.5"
gem "data_migrate"
gem "devise", "~> 4.9"
gem "devise_last_seen"
gem "dry-types", "~> 1.7"
gem "dry-initializer", "~> 3.1"
gem "dry-initializer-rails", "~> 3.1"
gem "fastimage", "~> 2.2"
gem "flipper"
gem "flipper-active_record"
gem "flipper-ui"
gem "friendly_id", "~> 5.5"
gem "geocoder", "~> 1.8"
gem "has_barcode", "~> 0.2"
gem "hightop", "~> 0.4"
gem "honeybadger", "~> 5.3"
gem "image_processing", "~> 1.12"
gem "jsbundling-rails", "~> 1.2"
gem "inline_svg", "~> 1.9"
gem "jbuilder", "~> 2.11"
gem "local_time", "~> 3.0"
gem "lockbox", "~> 1.3"
gem "maintenance_tasks", "~> 2.3"
gem "meilisearch-rails", "~> 0.13", github: "coder2000/meilisearch-rails"
gem "mjml-rails", "~> 4.9"
gem "money-rails", "~> 1.12"
gem "name_of_person", "~> 1.1", github: "basecamp/name_of_person"
gem "net-sftp", "~> 4.0"
gem "noticed", "~> 2.1"
gem "pagy", "~> 8"
gem "paper_trail", "~> 16"
gem "pg", "~> 1.5"
gem "pghero", "~> 3.4"
gem "pg_query", "~> 5.1"
gem "pretender", "~> 0.5"
gem "prosopite", "~> 1.4"
gem "pundit", "~> 2.3"
gem "puma", "~> 6.4"
gem "rails", "~> 8.0.2"
gem "rack-cors", "~> 2.0"
gem "redis", "~> 5.0"
gem "requestjs-rails", "~> 0.0"
gem "rorvswild"
gem "stimulus-rails", "~> 1.3"
gem "strong_migrations", "~> 1.6"
gem "sidekiq", "~> 7.2"
gem "sidekiq-cron", "~> 1.11"
gem "simple_calendar", "~> 3.0"
gem "sprockets-rails", "~> 3.4"
gem "tailwindcss-rails", "~> 4.2.3"
gem "turbo-rails", "~> 2.0"
gem "validates_timeliness"
gem "view_component", "~> 3"
gem "posthog-ruby"
gem "esbuild-rails"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", ">= 1.4.4", require: false

group :development, :test do
  gem "rspec-rails", "~> 6.0"
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem "byebug", platforms: %i[mri mingw x64_mingw]
  gem "factory_bot_rails"
  gem "faker"
  # use assigns in controller rspec
  gem "rails-controller-testing"
  gem "shoulda-matchers"
end

group :development do
  gem "annotaterb"
  gem "active_record_doctor"
  gem "better_errors"
  gem "binding_of_caller"
  gem "brakeman"
  gem "reek"
  # Access an interactive console on exception pages or by calling 'console' anywhere in the code.
  gem "web-console", ">= 4.1.0"
  # Display performance information such as SQL time and flame graphs for each request in your browser.
  # Can be configured to work on production as well see: https://github.com/MiniProfiler/rack-mini-profiler/blob/master/README.md
  gem "listen", "~> 3.3"

  gem "standard"

  gem "rails_real_favicon"
end

group :test do
  gem "capybara"
  gem "cuprite"
  gem "database_cleaner-active_record"
  gem "webmock"
  gem "launchy"
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[mingw mswin x64_mingw jruby]
